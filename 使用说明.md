# MediaPipe 人像裁剪工具

## 🎯 功能介绍
专门处理已经抠好图的人像，智能识别人体关键点，精确裁剪脖子或肩膀以下部分，只保留上半身。

## 💻 系统要求
- macOS (已在 M2 芯片上测试)
- Python 3.8+
- 8GB+ 内存

## 🚀 快速开始

### 方法一：双击启动 (推荐)
1. **放入图片**: 将已经抠好图的人像图片放到这个文件夹中
2. **双击运行**: 双击 `启动.py` 文件
3. **查看结果**: 程序会自动处理并保存结果

### 方法二：命令行启动
```bash
# 进入文件夹
cd /Users/<USER>/Desktop/裁剪

# 运行程序
python3 启动.py
```

### 方法三：终端启动 (高级用户)
```bash
# 双击 启动裁剪工具.command 文件
# 或在终端运行:
./启动裁剪工具.command
```

## 📁 支持的图片格式
- `.jpg` / `.jpeg`
- `.png` 
- `.bmp`
- `.tiff`

## 📊 输出文件说明

处理完成后，每张原图会生成以下文件：

| 文件名 | 说明 |
|--------|------|
| `原图名_cropped_neck.jpg` | 脖子位置裁剪 (最紧凑) |
| `原图名_cropped_shoulders.jpg` | 肩膀位置裁剪 (标准) |
| `原图名_cropped_chest.jpg` | 胸部位置裁剪 (宽松) |
| `原图名_debug.jpg` | 调试图，显示检测到的关键点 |
| `原图名_cropped_fallback.jpg` | 备用裁剪 (检测失败时) |

## 🎯 裁剪效果对比

### ✅ 适合的图片
- 已经抠好图的人像 (无背景或纯色背景)
- 正面、轻微侧身 (效果最佳)
- 人物清晰可见

### ⚠️ 可能效果一般的图片
- 完全侧身 (90度)
- 严重遮挡 (交叉双臂等)
- 背面人像
- 图片模糊或人物过小

### 🔧 处理策略
- **检测成功**: 使用AI精确定位关键点裁剪
- **检测失败**: 自动使用固定比例裁剪 (保留上60%)

## 📈 使用技巧

1. **选择最佳结果**: 程序会生成3种裁剪位置，选择最适合的
2. **查看调试图**: `*_debug.jpg` 显示检测到的关键点，帮助判断准确性
3. **批量处理**: 一次可以处理整个文件夹的图片
4. **备用方案**: 如果AI检测失败，会自动使用固定比例裁剪

## 🔧 故障排除

### 问题：程序无法启动
**解决方案：**
```bash
# 检查Python版本
python3 --version

# 手动安装依赖
python3 -m pip install opencv-python mediapipe numpy
```

### 问题：检测失败率高
**可能原因：**
- 图片中人物过小
- 姿势过于复杂
- 图片质量较差

**解决方案：**
- 使用更清晰的图片
- 选择正面或轻微侧身的图片
- 检查 `*_debug.jpg` 了解检测情况

### 问题：内存不足
**解决方案：**
- 减少同时处理的图片数量
- 降低图片分辨率
- 关闭其他占用内存的程序

## 📞 技术支持

如果遇到问题：
1. 查看终端输出的错误信息
2. 检查 `*_debug.jpg` 文件了解检测情况
3. 确保图片格式正确且未损坏

## 🎉 使用愉快！

这个工具专门为您的需求定制，希望能帮您高效处理人像裁剪任务！
