import cv2
import mediapipe as mp
import numpy as np
import os
from pathlib import Path

class MediaPipeCropper:
    def __init__(self):
        self.mp_pose = mp.solutions.pose
        self.pose = self.mp_pose.Pose(
            static_image_mode=True,
            model_complexity=2,  # 使用最高精度模型
            enable_segmentation=False,
            min_detection_confidence=0.5
        )
        self.mp_drawing = mp.solutions.drawing_utils
    
    def detect_crop_point(self, image):
        """检测裁剪点位置"""
        # 转换为RGB
        rgb_image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
        results = self.pose.process(rgb_image)
        
        if results.pose_landmarks:
            landmarks = results.pose_landmarks.landmark
            height, width = image.shape[:2]
            
            # 获取关键点坐标
            # 0: 鼻子, 11: 左肩, 12: 右肩
            nose = landmarks[0]
            left_shoulder = landmarks[11]
            right_shoulder = landmarks[12]
            
            # 计算肩膀中点
            shoulder_center_y = (left_shoulder.y + right_shoulder.y) / 2
            
            # 可以选择不同的裁剪点
            crop_options = {
                'neck': nose.y + (shoulder_center_y - nose.y) * 0.7,  # 脖子位置
                'shoulders': shoulder_center_y,  # 肩膀位置
                'chest': shoulder_center_y + (shoulder_center_y - nose.y) * 0.3  # 胸部位置
            }
            
            return crop_options, results.pose_landmarks
        
        return None, None
    
    def crop_image(self, image_path, crop_type='neck', save_result=True):
        """裁剪图像"""
        # 读取图像
        image = cv2.imread(image_path)
        if image is None:
            print(f"无法读取图像: {image_path}")
            return None
        
        height, width = image.shape[:2]
        print(f"原图尺寸: {width}x{height}")
        
        # 检测裁剪点
        crop_options, landmarks = self.detect_crop_point(image)
        
        if crop_options:
            crop_y = int(crop_options[crop_type] * height)
            print(f"检测成功! {crop_type}位置: y={crop_y}")
            
            # 裁剪图像
            cropped = image[:crop_y, :]
            
            if save_result:
                # 保存结果
                input_path = Path(image_path)
                output_path = input_path.parent / f"{input_path.stem}_cropped_{crop_type}{input_path.suffix}"
                cv2.imwrite(str(output_path), cropped)
                print(f"裁剪结果保存到: {output_path}")
                
                # 保存带关键点标注的原图
                annotated = image.copy()
                self.mp_drawing.draw_landmarks(
                    annotated, landmarks, self.mp_pose.POSE_CONNECTIONS)
                debug_path = input_path.parent / f"{input_path.stem}_debug{input_path.suffix}"
                cv2.imwrite(str(debug_path), annotated)
                print(f"调试图保存到: {debug_path}")
            
            return cropped
        else:
            print("检测失败! 使用备用策略...")
            # 备用策略：裁剪上60%
            crop_y = int(height * 0.6)
            cropped = image[:crop_y, :]
            
            if save_result:
                input_path = Path(image_path)
                output_path = input_path.parent / f"{input_path.stem}_cropped_fallback{input_path.suffix}"
                cv2.imwrite(str(output_path), cropped)
                print(f"备用裁剪结果保存到: {output_path}")
            
            return cropped

def test_single_image(image_path):
    """测试单张图片"""
    cropper = MediaPipeCropper()
    
    print(f"\n=== 测试图片: {image_path} ===")
    
    # 测试不同裁剪位置
    for crop_type in ['neck', 'shoulders', 'chest']:
        print(f"\n--- 测试 {crop_type} 裁剪 ---")
        result = cropper.crop_image(image_path, crop_type=crop_type)
        if result is not None:
            print(f"✅ {crop_type} 裁剪成功")
        else:
            print(f"❌ {crop_type} 裁剪失败")

def test_batch_images(folder_path):
    """批量测试图片"""
    cropper = MediaPipeCropper()

    # 支持的图片格式
    image_extensions = {'.jpg', '.jpeg', '.png', '.bmp', '.tiff'}

    folder = Path(folder_path)
    # 过滤掉已经处理过的图片
    image_files = []
    for f in folder.iterdir():
        if f.suffix.lower() in image_extensions:
            # 跳过已经裁剪过的图片
            if not any(keyword in f.name.lower() for keyword in ['_cropped_', '_debug']):
                image_files.append(f)

    if not image_files:
        print(f"在 {folder_path} 中没有找到未处理的图片文件")
        print("(已跳过包含 '_cropped_' 或 '_debug' 的文件)")
        return

    print(f"找到 {len(image_files)} 张未处理的图片")

    success_count = 0
    for image_file in image_files:
        print(f"\n处理: {image_file.name}")
        result = cropper.crop_image(str(image_file), crop_type='neck')
        if result is not None:
            success_count += 1

    print(f"\n=== 批量处理结果 ===")
    print(f"总计: {len(image_files)} 张")
    print(f"成功: {success_count} 张")
    print(f"失败: {len(image_files) - success_count} 张")
    print(f"成功率: {success_count/len(image_files)*100:.1f}%")

if __name__ == "__main__":
    print("MediaPipe 人像裁剪测试")
    print("=" * 50)

    # 自动检测当前文件夹中的图片
    current_dir = Path(".")
    image_extensions = {'.jpg', '.jpeg', '.png', '.bmp', '.tiff'}
    image_files = [f for f in current_dir.iterdir()
                   if f.suffix.lower() in image_extensions and f.is_file()]

    if image_files:
        print(f"发现 {len(image_files)} 张图片，开始自动测试...")
        test_batch_images(".")
    else:
        print("当前文件夹中没有发现图片文件")
        print("\n请将您的图片文件放到这个文件夹中，支持的格式:")
        print("- .jpg, .jpeg, .png, .bmp, .tiff")
        print("\n然后重新运行: python3 test_mediapipe_crop.py")

        print("\n或者手动测试单张图片:")
        print("python3 -c \"from test_mediapipe_crop import test_single_image; test_single_image('your_image.jpg')\"")
