#!/bin/bash

# MediaPipe 人像裁剪工具快捷启动脚本
# 双击即可运行

echo "🎯 MediaPipe 人像裁剪工具"
echo "=========================="

# 获取脚本所在目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
cd "$SCRIPT_DIR"

echo "📁 当前工作目录: $SCRIPT_DIR"

# 检查Python3是否可用
if ! command -v python3 &> /dev/null; then
    echo "❌ 错误: 未找到 python3"
    echo "请确保已安装 Python 3"
    read -p "按回车键退出..."
    exit 1
fi

echo "✅ Python3 已找到"

# 检查依赖包
echo "🔍 检查依赖包..."
python3 -c "import cv2, mediapipe, numpy" 2>/dev/null
if [ $? -ne 0 ]; then
    echo "⚠️  依赖包未安装，正在自动安装..."
    python3 -m pip install opencv-python mediapipe numpy
    if [ $? -ne 0 ]; then
        echo "❌ 依赖包安装失败"
        read -p "按回车键退出..."
        exit 1
    fi
    echo "✅ 依赖包安装完成"
else
    echo "✅ 依赖包已就绪"
fi

# 检查图片文件
echo "🖼️  检查图片文件..."
image_count=$(find . -maxdepth 1 -type f \( -iname "*.jpg" -o -iname "*.jpeg" -o -iname "*.png" -o -iname "*.bmp" -o -iname "*.tiff" \) | wc -l)

if [ $image_count -eq 0 ]; then
    echo "📂 当前文件夹中没有发现图片文件"
    echo ""
    echo "请将您已经抠好图的人像图片放到这个文件夹中："
    echo "$SCRIPT_DIR"
    echo ""
    echo "支持的格式: .jpg, .jpeg, .png, .bmp, .tiff"
    echo ""
    read -p "放好图片后按回车键继续，或直接关闭窗口..."
    
    # 重新检查
    image_count=$(find . -maxdepth 1 -type f \( -iname "*.jpg" -o -iname "*.jpeg" -o -iname "*.png" -o -iname "*.bmp" -o -iname "*.tiff" \) | wc -l)
    if [ $image_count -eq 0 ]; then
        echo "❌ 仍然没有发现图片文件"
        read -p "按回车键退出..."
        exit 1
    fi
fi

echo "✅ 发现 $image_count 张图片"

# 运行裁剪工具
echo ""
echo "🚀 开始处理图片..."
echo "=========================="

python3 test_mediapipe_crop.py

echo ""
echo "=========================="
echo "✅ 处理完成！"
echo ""
echo "📁 结果文件已保存在当前文件夹中："
echo "   - *_cropped_neck.jpg    (脖子位置裁剪)"
echo "   - *_cropped_shoulders.jpg (肩膀位置裁剪)" 
echo "   - *_cropped_chest.jpg   (胸部位置裁剪)"
echo "   - *_debug.jpg           (调试图，显示检测点)"
echo "   - *_cropped_fallback.jpg (备用裁剪，如果检测失败)"
echo ""

# 询问是否打开结果文件夹
read -p "是否打开结果文件夹查看？(y/n): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    open .
fi

echo ""
echo "🎉 感谢使用！"
read -p "按回车键退出..."
