#!/usr/bin/env python3
"""
MediaPipe 人像裁剪工具 - 智能拖拽版
拖拽图片到这个程序上，自动裁剪后保存到原位置
支持单张或批量处理
"""

import os
import sys
import subprocess
from pathlib import Path

def print_banner():
    print("🎯 MediaPipe 人像裁剪工具 - 智能拖拽版")
    print("=" * 60)
    print("🖼️  拖拽图片到程序上 → 自动裁剪 → 保存到原位置")
    print("📁 支持单张或批量处理，无需复制文件")
    print("=" * 60)

def check_dependencies():
    """检查并安装依赖"""
    print("🔍 检查依赖包...")
    
    required_packages = ['cv2', 'mediapipe', 'numpy']
    missing_packages = []
    
    for package in required_packages:
        try:
            if package == 'cv2':
                import cv2
            elif package == 'mediapipe':
                import mediapipe
            elif package == 'numpy':
                import numpy
            print(f"✅ {package} 已安装")
        except ImportError:
            missing_packages.append(package)
            print(f"❌ {package} 未安装")
    
    if missing_packages:
        print("\n⚠️  正在安装缺失的依赖包...")
        pip_packages = {
            'cv2': 'opencv-python',
            'mediapipe': 'mediapipe', 
            'numpy': 'numpy'
        }
        
        for package in missing_packages:
            pip_name = pip_packages[package]
            print(f"安装 {pip_name}...")
            result = subprocess.run([sys.executable, '-m', 'pip', 'install', pip_name], 
                                  capture_output=True, text=True)
            if result.returncode == 0:
                print(f"✅ {pip_name} 安装成功")
            else:
                print(f"❌ {pip_name} 安装失败: {result.stderr}")
                return False
    
    print("✅ 所有依赖包已就绪")
    return True

def get_dragged_files():
    """获取拖拽的文件路径"""
    print("\n🖼️  检查输入文件...")

    # 检查命令行参数（拖拽文件）
    if len(sys.argv) > 1:
        file_paths = []
        image_extensions = {'.jpg', '.jpeg', '.png', '.bmp', '.tiff'}

        for arg in sys.argv[1:]:
            file_path = Path(arg)
            if file_path.exists() and file_path.is_file():
                if file_path.suffix.lower() in image_extensions:
                    file_paths.append(file_path)
                else:
                    print(f"⚠️  跳过非图片文件: {file_path.name}")
            else:
                print(f"⚠️  文件不存在: {arg}")

        if file_paths:
            print(f"✅ 接收到 {len(file_paths)} 张图片:")
            for i, fp in enumerate(file_paths[:5]):
                print(f"   {i+1}. {fp.name} ({fp.parent})")
            if len(file_paths) > 5:
                print(f"   ... 还有 {len(file_paths) - 5} 张")
            return file_paths
        else:
            print("❌ 没有有效的图片文件")
            return None

    # 没有拖拽文件，提示用户
    print("📋 使用方法:")
    print("   1. 把图片文件拖拽到这个程序上")
    print("   2. 或者在终端运行: python3 启动.py 图片路径")
    print("   3. 支持批量处理多张图片")
    print(f"\n📁 支持格式: .jpg, .jpeg, .png, .bmp, .tiff")

    input("\n请拖拽图片到程序上，然后按回车键退出...")
    return None

def process_images(file_paths):
    """处理图片文件"""
    print(f"\n🚀 开始处理 {len(file_paths)} 张图片...")
    print("=" * 60)

    try:
        # 导入裁剪工具
        from test_mediapipe_crop import MediaPipeCropper
        cropper = MediaPipeCropper()

        success_count = 0
        results = []

        for i, file_path in enumerate(file_paths, 1):
            print(f"\n[{i}/{len(file_paths)}] 处理: {file_path.name}")
            print(f"📁 位置: {file_path.parent}")

            # 裁剪图片，结果保存到原位置
            result = cropper.crop_image(str(file_path), crop_type='neck')

            if result is not None:
                success_count += 1
                results.append(f"✅ {file_path.name}")
                print(f"✅ 成功! 结果已保存到原位置")
            else:
                results.append(f"❌ {file_path.name}")
                print(f"❌ 处理失败")

        print("\n" + "=" * 60)
        print("📊 处理结果:")
        for result in results:
            print(f"   {result}")

        print(f"\n✅ 成功: {success_count}/{len(file_paths)} 张")
        print(f"📁 所有结果已保存到图片原位置")

        return success_count > 0

    except Exception as e:
        print(f"❌ 处理过程中出现错误: {e}")
        print("\n请检查:")
        print("1. 图片文件是否损坏")
        print("2. 图片是否为有效的人像图片")
        print("3. 是否有足够的磁盘空间")
        print("4. 是否有写入权限")
        return False

def main():
    """主函数"""
    print_banner()
    
    # 检查依赖
    if not check_dependencies():
        input("按回车键退出...")
        return
    
    # 检查图片
    if not check_images():
        return
    
    # 运行裁剪
    if run_cropping():
        # 询问是否打开文件夹
        try:
            response = input("\n是否打开结果文件夹查看？(y/n): ").lower().strip()
            if response in ['y', 'yes', '是', 'Y']:
                if sys.platform == 'darwin':  # macOS
                    subprocess.run(['open', '.'])
                elif sys.platform == 'win32':  # Windows
                    subprocess.run(['explorer', '.'])
                else:  # Linux
                    subprocess.run(['xdg-open', '.'])
        except:
            pass
    
    print("\n🎉 感谢使用MediaPipe人像裁剪工具！")
    input("按回车键退出...")

if __name__ == "__main__":
    main()
