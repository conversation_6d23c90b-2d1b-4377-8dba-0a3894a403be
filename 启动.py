#!/usr/bin/env python3
"""
MediaPipe 人像裁剪工具 - 快捷启动器
双击运行或在终端执行: python3 启动.py
"""

import os
import sys
import subprocess
from pathlib import Path

def print_banner():
    print("🎯 MediaPipe 人像裁剪工具")
    print("=" * 50)
    print("专门处理已抠图的人像，智能裁剪脖子/肩膀以下部分")
    print("=" * 50)

def check_dependencies():
    """检查并安装依赖"""
    print("🔍 检查依赖包...")
    
    required_packages = ['cv2', 'mediapipe', 'numpy']
    missing_packages = []
    
    for package in required_packages:
        try:
            if package == 'cv2':
                import cv2
            elif package == 'mediapipe':
                import mediapipe
            elif package == 'numpy':
                import numpy
            print(f"✅ {package} 已安装")
        except ImportError:
            missing_packages.append(package)
            print(f"❌ {package} 未安装")
    
    if missing_packages:
        print("\n⚠️  正在安装缺失的依赖包...")
        pip_packages = {
            'cv2': 'opencv-python',
            'mediapipe': 'mediapipe', 
            'numpy': 'numpy'
        }
        
        for package in missing_packages:
            pip_name = pip_packages[package]
            print(f"安装 {pip_name}...")
            result = subprocess.run([sys.executable, '-m', 'pip', 'install', pip_name], 
                                  capture_output=True, text=True)
            if result.returncode == 0:
                print(f"✅ {pip_name} 安装成功")
            else:
                print(f"❌ {pip_name} 安装失败: {result.stderr}")
                return False
    
    print("✅ 所有依赖包已就绪")
    return True

def check_images():
    """检查图片文件"""
    print("\n🖼️  检查图片文件...")
    
    current_dir = Path(".")
    image_extensions = {'.jpg', '.jpeg', '.png', '.bmp', '.tiff'}
    image_files = [f for f in current_dir.iterdir() 
                   if f.suffix.lower() in image_extensions and f.is_file()]
    
    if not image_files:
        print("📂 当前文件夹中没有发现图片文件")
        print(f"\n请将您已经抠好图的人像图片放到这个文件夹中：")
        print(f"{current_dir.absolute()}")
        print(f"\n支持的格式: {', '.join(image_extensions)}")
        
        input("\n放好图片后按回车键继续...")
        
        # 重新检查
        image_files = [f for f in current_dir.iterdir() 
                       if f.suffix.lower() in image_extensions and f.is_file()]
        
        if not image_files:
            print("❌ 仍然没有发现图片文件")
            input("按回车键退出...")
            return False
    
    print(f"✅ 发现 {len(image_files)} 张图片: {[f.name for f in image_files[:3]]}{'...' if len(image_files) > 3 else ''}")
    return True

def run_cropping():
    """运行裁剪工具"""
    print("\n🚀 开始处理图片...")
    print("=" * 50)
    
    try:
        # 导入并运行裁剪工具
        from test_mediapipe_crop import test_batch_images
        test_batch_images(".")
        
        print("\n" + "=" * 50)
        print("✅ 处理完成！")
        
        print("\n📁 结果文件已保存在当前文件夹中：")
        print("   - *_cropped_neck.jpg      (脖子位置裁剪)")
        print("   - *_cropped_shoulders.jpg (肩膀位置裁剪)")
        print("   - *_cropped_chest.jpg     (胸部位置裁剪)")
        print("   - *_debug.jpg             (调试图，显示检测点)")
        print("   - *_cropped_fallback.jpg  (备用裁剪，检测失败时)")
        
        return True
        
    except Exception as e:
        print(f"❌ 处理过程中出现错误: {e}")
        print("\n请检查:")
        print("1. 图片文件是否损坏")
        print("2. 图片是否为有效的人像图片")
        print("3. 是否有足够的磁盘空间")
        return False

def main():
    """主函数"""
    print_banner()
    
    # 检查依赖
    if not check_dependencies():
        input("按回车键退出...")
        return
    
    # 检查图片
    if not check_images():
        return
    
    # 运行裁剪
    if run_cropping():
        # 询问是否打开文件夹
        try:
            response = input("\n是否打开结果文件夹查看？(y/n): ").lower().strip()
            if response in ['y', 'yes', '是', 'Y']:
                if sys.platform == 'darwin':  # macOS
                    subprocess.run(['open', '.'])
                elif sys.platform == 'win32':  # Windows
                    subprocess.run(['explorer', '.'])
                else:  # Linux
                    subprocess.run(['xdg-open', '.'])
        except:
            pass
    
    print("\n🎉 感谢使用MediaPipe人像裁剪工具！")
    input("按回车键退出...")

if __name__ == "__main__":
    main()
