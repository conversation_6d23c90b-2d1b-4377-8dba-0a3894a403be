#!/usr/bin/env python3
"""
智能人像裁剪工具
拖拽图片到这个程序上，自动裁剪并保存到原位置
"""

import sys
import subprocess
from pathlib import Path

def main():
    print("🎯 智能人像裁剪工具")
    print("=" * 40)
    
    # 检查是否有拖拽的文件
    if len(sys.argv) < 2:
        print("❌ 没有接收到图片文件")
        print("\n使用方法:")
        print("1. 把图片拖拽到这个程序上")
        print("2. 支持 .jpg .png .jpeg .bmp .tiff 格式")
        input("\n按回车键退出...")
        return
    
    # 检查依赖
    try:
        import cv2
        import mediapipe as mp
        import numpy as np
    except ImportError as e:
        print(f"❌ 缺少依赖: {e}")
        print("正在安装...")
        packages = {'cv2': 'opencv-python', 'mediapipe': 'mediapipe', 'numpy': 'numpy'}
        for pkg_name, pip_name in packages.items():
            try:
                __import__(pkg_name)
            except ImportError:
                subprocess.run([sys.executable, '-m', 'pip', 'install', pip_name])
        print("✅ 依赖安装完成，请重新运行程序")
        input("按回车键退出...")
        return
    
    # 获取图片文件
    image_files = []
    image_extensions = {'.jpg', '.jpeg', '.png', '.bmp', '.tiff'}
    
    for arg in sys.argv[1:]:
        file_path = Path(arg)
        if file_path.exists() and file_path.suffix.lower() in image_extensions:
            image_files.append(file_path)
        else:
            print(f"⚠️  跳过: {arg}")
    
    if not image_files:
        print("❌ 没有有效的图片文件")
        input("按回车键退出...")
        return
    
    print(f"✅ 找到 {len(image_files)} 张图片")
    
    # 导入裁剪工具
    try:
        from test_mediapipe_crop import MediaPipeCropper
        cropper = MediaPipeCropper()
    except ImportError:
        print("❌ 找不到裁剪工具模块")
        input("按回车键退出...")
        return
    
    # 处理每张图片
    success = 0
    for i, img_path in enumerate(image_files, 1):
        print(f"\n[{i}/{len(image_files)}] 处理: {img_path.name}")
        
        try:
            result = cropper.crop_image(str(img_path), crop_type='neck')
            if result is not None:
                success += 1
                print(f"✅ 成功! 保存到: {img_path.parent}")
            else:
                print(f"❌ 失败")
        except Exception as e:
            print(f"❌ 错误: {e}")
    
    print(f"\n🎉 完成! 成功处理 {success}/{len(image_files)} 张图片")
    
    # 打开文件夹
    if success > 0:
        try:
            response = input("\n打开结果文件夹? (y/n): ").lower()
            if response in ['y', 'yes', '是']:
                folder = image_files[0].parent
                if sys.platform == 'darwin':
                    subprocess.run(['open', str(folder)])
                elif sys.platform == 'win32':
                    subprocess.run(['explorer', str(folder)])
        except:
            pass
    
    input("\n按回车键退出...")

if __name__ == "__main__":
    main()
