#!/usr/bin/env python3
"""
创建可拖拽的macOS应用程序
"""

import os
import shutil
from pathlib import Path

def create_app():
    """创建macOS应用程序包"""
    
    app_name = "智能人像裁剪"
    app_path = Path(f"{app_name}.app")
    
    # 删除已存在的应用
    if app_path.exists():
        shutil.rmtree(app_path)
    
    # 创建应用程序目录结构
    contents_dir = app_path / "Contents"
    macos_dir = contents_dir / "MacOS"
    resources_dir = contents_dir / "Resources"
    
    contents_dir.mkdir(parents=True)
    macos_dir.mkdir()
    resources_dir.mkdir()
    
    # 创建Info.plist
    info_plist = '''<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
    <key>CFBundleExecutable</key>
    <string>智能人像裁剪</string>
    <key>CFBundleIdentifier</key>
    <string>com.local.智能人像裁剪</string>
    <key>CFBundleName</key>
    <string>智能人像裁剪</string>
    <key>CFBundleVersion</key>
    <string>1.0</string>
    <key>CFBundleShortVersionString</key>
    <string>1.0</string>
    <key>CFBundleDocumentTypes</key>
    <array>
        <dict>
            <key>CFBundleTypeExtensions</key>
            <array>
                <string>jpg</string>
                <string>jpeg</string>
                <string>png</string>
                <string>bmp</string>
                <string>tiff</string>
            </array>
            <key>CFBundleTypeName</key>
            <string>Image</string>
            <key>CFBundleTypeRole</key>
            <string>Viewer</string>
        </dict>
    </array>
</dict>
</plist>'''
    
    with open(contents_dir / "Info.plist", "w") as f:
        f.write(info_plist)
    
    # 创建可执行脚本
    executable_script = '''#!/bin/bash
cd "$(dirname "$0")"
cd ../../..
python3 智能裁剪.py "$@"
'''
    
    executable_path = macos_dir / "智能人像裁剪"
    with open(executable_path, "w") as f:
        f.write(executable_script)
    
    # 设置可执行权限
    os.chmod(executable_path, 0o755)
    
    print(f"✅ 应用程序创建成功: {app_path}")
    print("\n📋 使用方法:")
    print("1. 把图片拖拽到应用程序图标上")
    print("2. 应用程序会自动打开终端处理图片")
    print("3. 处理完成后结果保存在图片原位置")
    
    return True

if __name__ == "__main__":
    create_app()
