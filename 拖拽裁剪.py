#!/usr/bin/env python3
"""
拖拽裁剪工具
支持拖拽任意位置的图片或文件夹到程序上
自动裁剪后保存到原位置
"""

import sys
import subprocess
from pathlib import Path

def print_banner():
    print("🎯 拖拽智能人像裁剪工具")
    print("=" * 50)
    print("📁 拖拽图片/文件夹 → 自动裁剪 → 保存到原位置")
    print("🖼️  支持单张、批量、任意位置处理")
    print("=" * 50)

def check_dependencies():
    """检查依赖包"""
    print("\n🔍 检查依赖包...")
    
    required_packages = ['cv2', 'mediapipe', 'numpy']
    missing_packages = []
    
    for package in required_packages:
        try:
            if package == 'cv2':
                import cv2
            elif package == 'mediapipe':
                import mediapipe
            elif package == 'numpy':
                import numpy
            print(f"✅ {package} 已安装")
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        print(f"❌ 缺少依赖包: {missing_packages}")
        print("正在自动安装...")
        
        package_map = {
            'cv2': 'opencv-python',
            'mediapipe': 'mediapipe', 
            'numpy': 'numpy'
        }
        
        for pkg in missing_packages:
            pip_name = package_map[pkg]
            try:
                subprocess.run([sys.executable, '-m', 'pip', 'install', pip_name], 
                             check=True, capture_output=True)
                print(f"✅ {pkg} 安装成功")
            except subprocess.CalledProcessError:
                print(f"❌ {pkg} 安装失败")
                return False
    
    print("✅ 所有依赖包已就绪")
    return True

def get_dragged_files():
    """获取拖拽的文件路径"""
    print("\n🖼️  检查输入文件...")
    
    # 检查命令行参数（拖拽文件）
    if len(sys.argv) > 1:
        file_paths = []
        image_extensions = {'.jpg', '.jpeg', '.png', '.bmp', '.tiff'}
        
        for arg in sys.argv[1:]:
            path = Path(arg)
            
            if path.is_file():
                # 单个文件
                if path.suffix.lower() in image_extensions:
                    file_paths.append(path)
                else:
                    print(f"⚠️  跳过非图片文件: {path.name}")
            elif path.is_dir():
                # 文件夹 - 查找所有图片
                print(f"📁 扫描文件夹: {path}")
                folder_images = []
                for ext in image_extensions:
                    folder_images.extend(path.glob(f"*{ext}"))
                    folder_images.extend(path.glob(f"*{ext.upper()}"))
                
                # 过滤掉已处理的图片
                folder_images = [img for img in folder_images if '_cropped_' not in img.name and '_debug' not in img.name]
                
                if folder_images:
                    file_paths.extend(folder_images)
                    print(f"   找到 {len(folder_images)} 张图片")
                else:
                    print(f"   文件夹中没有找到图片")
            else:
                print(f"⚠️  路径不存在: {arg}")
        
        if file_paths:
            print(f"\n✅ 总共接收到 {len(file_paths)} 张图片:")
            for i, fp in enumerate(file_paths[:5]):
                print(f"   {i+1}. {fp.name}")
                print(f"       位置: {fp.parent}")
            if len(file_paths) > 5:
                print(f"   ... 还有 {len(file_paths) - 5} 张")
            return file_paths
        else:
            print("❌ 没有有效的图片文件")
            return None
    
    # 没有拖拽文件，提示用户
    print("📋 使用方法:")
    print("   1. 拖拽图片文件到这个程序上")
    print("   2. 拖拽文件夹到这个程序上（批量处理）")
    print("   3. 同时拖拽多个文件/文件夹")
    print("   4. 支持桌面、下载、任意位置的图片")
    print(f"\n📁 支持格式: .jpg, .jpeg, .png, .bmp, .tiff")
    
    input("\n请拖拽图片或文件夹到程序上，然后按回车键退出...")
    return None

def process_images(file_paths):
    """处理图片文件"""
    print(f"\n🚀 开始处理 {len(file_paths)} 张图片...")
    print("=" * 60)
    
    try:
        # 导入裁剪工具
        from test_mediapipe_crop import MediaPipeCropper
        cropper = MediaPipeCropper()
        
        success_count = 0
        results = []
        
        for i, file_path in enumerate(file_paths, 1):
            print(f"\n[{i}/{len(file_paths)}] 处理: {file_path.name}")
            print(f"📁 位置: {file_path.parent}")
            
            # 裁剪图片，结果保存到原位置
            result = cropper.crop_image(str(file_path), crop_type='neck')
            
            if result is not None:
                success_count += 1
                results.append(f"✅ {file_path.name}")
                print(f"✅ 成功! 结果已保存到原位置")
            else:
                results.append(f"❌ {file_path.name}")
                print(f"❌ 处理失败")
        
        print("\n" + "=" * 60)
        print("📊 处理结果:")
        for result in results:
            print(f"   {result}")
        
        print(f"\n✅ 成功: {success_count}/{len(file_paths)} 张")
        print(f"📁 所有结果已保存到图片原位置")
        
        return success_count > 0
        
    except Exception as e:
        print(f"❌ 处理过程中出现错误: {e}")
        print("\n请检查:")
        print("1. 图片文件是否损坏")
        print("2. 图片是否为有效的人像图片")
        print("3. 是否有足够的磁盘空间")
        print("4. 是否有写入权限")
        return False

def main():
    """主函数"""
    print_banner()
    
    # 检查依赖
    if not check_dependencies():
        input("按回车键退出...")
        return
    
    # 获取拖拽的文件
    file_paths = get_dragged_files()
    if not file_paths:
        return
    
    # 处理图片
    if process_images(file_paths):
        # 询问是否打开第一张图片的文件夹
        try:
            first_folder = file_paths[0].parent
            response = input(f"\n是否打开结果文件夹查看？\n📁 {first_folder}\n(y/n): ").lower().strip()
            if response in ['y', 'yes', '是', 'Y']:
                if sys.platform == 'darwin':  # macOS
                    subprocess.run(['open', str(first_folder)])
                elif sys.platform == 'win32':  # Windows
                    subprocess.run(['explorer', str(first_folder)])
                else:  # Linux
                    subprocess.run(['xdg-open', str(first_folder)])
        except:
            pass
    
    print("\n🎉 感谢使用拖拽智能人像裁剪工具！")
    input("按回车键退出...")

if __name__ == "__main__":
    main()
