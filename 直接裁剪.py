#!/usr/bin/env python3
"""
直接裁剪工具 - 处理当前文件夹中的所有图片
"""

import os
import sys
from pathlib import Path

def main():
    print("🎯 直接裁剪工具")
    print("=" * 40)
    
    # 检查依赖
    try:
        import cv2
        import mediapipe as mp
        import numpy as np
        print("✅ 依赖包检查完成")
    except ImportError as e:
        print(f"❌ 缺少依赖: {e}")
        return
    
    # 获取当前目录的所有图片
    current_dir = Path(".")
    image_extensions = {'.jpg', '.jpeg', '.png', '.bmp', '.tiff'}
    
    all_images = []
    for ext in image_extensions:
        all_images.extend(current_dir.glob(f"*{ext}"))
        all_images.extend(current_dir.glob(f"*{ext.upper()}"))
    
    # 过滤掉已处理的图片
    images = [img for img in all_images if '_cropped_' not in img.name and '_debug' not in img.name]
    
    if not images:
        print("❌ 当前文件夹没有找到图片文件")
        print(f"📁 当前位置: {current_dir.absolute()}")
        print("请把图片放到这个文件夹中，然后重新运行")
        return
    
    print(f"✅ 找到 {len(images)} 张图片:")
    for i, img in enumerate(images[:5], 1):
        print(f"   {i}. {img.name}")
    if len(images) > 5:
        print(f"   ... 还有 {len(images) - 5} 张")
    
    # 导入裁剪工具
    try:
        from test_mediapipe_crop import MediaPipeCropper
        cropper = MediaPipeCropper()
        print("✅ 裁剪工具加载成功")
    except ImportError:
        print("❌ 找不到裁剪工具")
        return
    
    # 处理每张图片
    print(f"\n🚀 开始处理...")
    success = 0
    
    for i, img_path in enumerate(images, 1):
        print(f"\n[{i}/{len(images)}] {img_path.name}")
        
        try:
            # 直接调用裁剪
            result = cropper.crop_image(str(img_path), crop_type='neck', save_result=True)
            
            if result is not None:
                success += 1
                print(f"✅ 成功")
            else:
                print(f"❌ 失败")
                
        except Exception as e:
            print(f"❌ 错误: {e}")
    
    print(f"\n🎉 处理完成!")
    print(f"✅ 成功: {success}/{len(images)} 张")
    
    if success > 0:
        print(f"\n📁 结果文件:")
        result_files = list(current_dir.glob("*_cropped_*.jpg")) + list(current_dir.glob("*_cropped_*.png"))
        for rf in result_files[:5]:
            print(f"   {rf.name}")
        if len(result_files) > 5:
            print(f"   ... 还有 {len(result_files) - 5} 个")
    
    print("\n🎉 处理完成！")

if __name__ == "__main__":
    main()
