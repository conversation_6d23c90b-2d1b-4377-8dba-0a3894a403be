#!/usr/bin/env python3
"""
MediaPipe 人像裁剪工具安装脚本
适用于 macOS M2 芯片
"""

import subprocess
import sys
import os

def run_command(command):
    """运行命令并显示输出"""
    print(f"执行: {command}")
    try:
        result = subprocess.run(command, shell=True, check=True, 
                              capture_output=True, text=True)
        print("✅ 成功")
        if result.stdout:
            print(result.stdout)
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ 失败: {e}")
        if e.stderr:
            print(f"错误信息: {e.stderr}")
        return False

def check_python_version():
    """检查Python版本"""
    version = sys.version_info
    print(f"Python版本: {version.major}.{version.minor}.{version.micro}")
    
    if version.major < 3 or (version.major == 3 and version.minor < 8):
        print("❌ 需要Python 3.8或更高版本")
        return False
    
    print("✅ Python版本符合要求")
    return True

def install_packages():
    """安装必要的包"""
    packages = [
        "opencv-python",
        "mediapipe", 
        "numpy",
        "pathlib"  # Python 3.4+自带，但确保可用
    ]
    
    print("开始安装依赖包...")
    
    for package in packages:
        print(f"\n安装 {package}...")
        if not run_command(f"pip install {package}"):
            print(f"❌ {package} 安装失败")
            return False
    
    print("\n✅ 所有依赖包安装完成!")
    return True

def test_installation():
    """测试安装是否成功"""
    print("\n测试安装...")
    
    try:
        import cv2
        print(f"✅ OpenCV版本: {cv2.__version__}")
    except ImportError:
        print("❌ OpenCV导入失败")
        return False
    
    try:
        import mediapipe as mp
        print(f"✅ MediaPipe版本: {mp.__version__}")
    except ImportError:
        print("❌ MediaPipe导入失败")
        return False
    
    try:
        import numpy as np
        print(f"✅ NumPy版本: {np.__version__}")
    except ImportError:
        print("❌ NumPy导入失败")
        return False
    
    print("✅ 所有包导入成功!")
    return True

def main():
    print("MediaPipe 人像裁剪工具安装程序")
    print("=" * 50)
    
    # 检查Python版本
    if not check_python_version():
        return
    
    # 安装包
    if not install_packages():
        print("\n❌ 安装失败，请检查错误信息")
        return
    
    # 测试安装
    if not test_installation():
        print("\n❌ 安装验证失败")
        return
    
    print("\n" + "=" * 50)
    print("🎉 安装完成!")
    print("\n下一步:")
    print("1. 准备一些已经抠好图的人像图片")
    print("2. 运行: python test_mediapipe_crop.py")
    print("3. 修改代码中的图片路径进行测试")
    
    print("\n使用示例:")
    print("test_single_image('your_image.jpg')  # 测试单张图片")
    print("test_batch_images('./images')        # 批量测试文件夹")

if __name__ == "__main__":
    main()
