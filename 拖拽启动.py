#!/usr/bin/env python3
"""
MediaPipe 人像裁剪工具 - 支持拖拽版本
支持：
1. 拖拽单个图片文件到脚本上
2. 拖拽多个图片文件到脚本上  
3. 拖拽文件夹到脚本上
4. 直接双击运行（处理当前文件夹）
"""

import os
import sys
import shutil
import subprocess
from pathlib import Path

def print_banner():
    print("🎯 MediaPipe 人像裁剪工具 (支持拖拽)")
    print("=" * 60)
    print("使用方法:")
    print("1. 拖拽图片文件到这个脚本上")
    print("2. 拖拽文件夹到这个脚本上") 
    print("3. 直接双击运行")
    print("=" * 60)

def check_dependencies():
    """检查并安装依赖"""
    print("🔍 检查依赖包...")
    
    required_packages = ['cv2', 'mediapipe', 'numpy']
    missing_packages = []
    
    for package in required_packages:
        try:
            if package == 'cv2':
                import cv2
            elif package == 'mediapipe':
                import mediapipe
            elif package == 'numpy':
                import numpy
            print(f"✅ {package} 已安装")
        except ImportError:
            missing_packages.append(package)
            print(f"❌ {package} 未安装")
    
    if missing_packages:
        print("\n⚠️  正在安装缺失的依赖包...")
        pip_packages = {
            'cv2': 'opencv-python',
            'mediapipe': 'mediapipe', 
            'numpy': 'numpy'
        }
        
        for package in missing_packages:
            pip_name = pip_packages[package]
            print(f"安装 {pip_name}...")
            result = subprocess.run([sys.executable, '-m', 'pip', 'install', pip_name], 
                                  capture_output=True, text=True)
            if result.returncode == 0:
                print(f"✅ {pip_name} 安装成功")
            else:
                print(f"❌ {pip_name} 安装失败: {result.stderr}")
                return False
    
    print("✅ 所有依赖包已就绪")
    return True

def get_image_files_from_args():
    """从命令行参数获取图片文件"""
    image_extensions = {'.jpg', '.jpeg', '.png', '.bmp', '.tiff'}
    image_files = []
    folders = []
    
    # 处理拖拽的文件/文件夹
    for arg in sys.argv[1:]:
        path = Path(arg)
        if path.is_file() and path.suffix.lower() in image_extensions:
            image_files.append(path)
        elif path.is_dir():
            folders.append(path)
    
    # 从文件夹中收集图片
    for folder in folders:
        folder_images = [f for f in folder.iterdir() 
                        if f.is_file() and f.suffix.lower() in image_extensions]
        image_files.extend(folder_images)
    
    return image_files

def copy_images_to_current_dir(image_files):
    """将图片复制到当前目录"""
    current_dir = Path(".")
    copied_files = []
    
    print(f"\n📁 复制 {len(image_files)} 张图片到工作目录...")
    
    for img_file in image_files:
        # 生成唯一的文件名，避免覆盖
        target_name = img_file.name
        target_path = current_dir / target_name
        
        counter = 1
        while target_path.exists():
            name_parts = img_file.stem, counter, img_file.suffix
            target_name = f"{name_parts[0]}_{name_parts[1]}{name_parts[2]}"
            target_path = current_dir / target_name
            counter += 1
        
        try:
            shutil.copy2(img_file, target_path)
            copied_files.append(target_path)
            print(f"✅ {img_file.name} -> {target_name}")
        except Exception as e:
            print(f"❌ 复制失败 {img_file.name}: {e}")
    
    return copied_files

def get_local_images():
    """获取当前目录的图片"""
    current_dir = Path(".")
    image_extensions = {'.jpg', '.jpeg', '.png', '.bmp', '.tiff'}
    return [f for f in current_dir.iterdir() 
            if f.suffix.lower() in image_extensions and f.is_file()]

def run_cropping(target_dir="."):
    """运行裁剪工具"""
    print(f"\n🚀 开始处理图片...")
    print("=" * 50)
    
    try:
        # 导入并运行裁剪工具
        from test_mediapipe_crop import test_batch_images
        test_batch_images(target_dir)
        
        print("\n" + "=" * 50)
        print("✅ 处理完成！")
        
        print("\n📁 结果文件已保存：")
        print("   - *_cropped_neck.jpg      (脖子位置裁剪)")
        print("   - *_cropped_shoulders.jpg (肩膀位置裁剪)")
        print("   - *_cropped_chest.jpg     (胸部位置裁剪)")
        print("   - *_debug.jpg             (调试图，显示检测点)")
        print("   - *_cropped_fallback.jpg  (备用裁剪)")
        
        return True
        
    except Exception as e:
        print(f"❌ 处理过程中出现错误: {e}")
        return False

def main():
    """主函数"""
    print_banner()
    
    # 检查依赖
    if not check_dependencies():
        input("按回车键退出...")
        return
    
    # 获取拖拽的图片文件
    dragged_images = get_image_files_from_args()
    
    if dragged_images:
        print(f"\n🎯 检测到拖拽的 {len(dragged_images)} 张图片:")
        for img in dragged_images[:5]:  # 只显示前5个
            print(f"   📷 {img.name}")
        if len(dragged_images) > 5:
            print(f"   ... 还有 {len(dragged_images) - 5} 张")
        
        # 复制图片到当前目录
        copied_files = copy_images_to_current_dir(dragged_images)
        
        if copied_files:
            print(f"\n✅ 成功复制 {len(copied_files)} 张图片")
        else:
            print("❌ 没有成功复制任何图片")
            input("按回车键退出...")
            return
    else:
        # 没有拖拽文件，检查当前目录
        local_images = get_local_images()
        
        if not local_images:
            print("\n📂 使用方法:")
            print("1. 拖拽图片文件到这个脚本上")
            print("2. 拖拽包含图片的文件夹到这个脚本上")
            print("3. 将图片放到当前文件夹后双击运行")
            print(f"\n当前文件夹: {Path('.').absolute()}")
            print(f"支持格式: .jpg, .jpeg, .png, .bmp, .tiff")
            
            input("\n按回车键退出...")
            return
        else:
            print(f"\n✅ 当前目录发现 {len(local_images)} 张图片")
    
    # 运行裁剪
    if run_cropping():
        # 询问是否打开文件夹
        try:
            response = input("\n是否打开结果文件夹查看？(y/n): ").lower().strip()
            if response in ['y', 'yes', '是', 'Y']:
                if sys.platform == 'darwin':  # macOS
                    subprocess.run(['open', '.'])
                elif sys.platform == 'win32':  # Windows
                    subprocess.run(['explorer', '.'])
                else:  # Linux
                    subprocess.run(['xdg-open', '.'])
        except:
            pass
    
    print("\n🎉 感谢使用！")
    input("按回车键退出...")

if __name__ == "__main__":
    main()
